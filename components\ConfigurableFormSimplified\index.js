/**
 * ConfigurableFormSimplified 简化版组件
 * 基于 ConfigurableForm 的简化版本，相当于原来的 huntCopy
 */

import createConfigurableBehavior from '../ConfigurableForm/core/behavior';
import { CONFIG_TYPES } from '../ConfigurableForm/config/constants';

Component({
  behaviors: [createConfigurableBehavior({
    variant: CONFIG_TYPES.SIMPLIFIED,
    enableVipCheck: true,
    enableSearch: true
  })],
  
  properties: {
    // 继承父组件的所有属性，但默认值为简化版
    variant: {
      type: String,
      value: CONFIG_TYPES.SIMPLIFIED
    },
    
    wrapHeight: {
      type: String,
      value: ''
    },
    
    isPage: {
      type: Boolean,
      value: false
    },
    
    // 简化版默认排除的字段
    excludeFields: {
      type: Array,
      value: ['chain_codes'] // 排除产业链相关字段
    },
    
    enableVipCheck: {
      type: Boolean,
      value: true
    },
    
    enableSearch: {
      type: Boolean,
      value: true
    }
  },

  data: {
    // 简化版特有的数据
    simplified: true
  },

  lifetimes: {
    attached() {
      console.log('ConfigurableFormSimplified attached');
      // 触发简化版初始化事件
      this.triggerEvent('ready', {
        variant: CONFIG_TYPES.SIMPLIFIED,
        simplified: true
      });
    }
  },

  methods: {
    /**
     * 简化版特有的方法可以在这里添加
     * 大部分方法都继承自 behavior
     */
    
    /**
     * 获取简化版表单数据
     */
    getSimplifiedFormData() {
      const baseData = this.getFormData();
      return {
        ...baseData,
        simplified: true,
        excludedFields: this.data.excludeFields
      };
    },

    /**
     * 简化版的清空方法
     */
    clearSimplifiedForm() {
      this.clearSear();
      console.log('Simplified form cleared');
    }
  }
});
