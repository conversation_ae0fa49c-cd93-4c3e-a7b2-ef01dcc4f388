/**
 * 统一的 Behavior 基类
 * 创建可配置的基础 Behavior，支持不同变体
 */

const {
  getSearchConfig,
  CONFIG_TYPES,
  DEFAULT_PARAMS
} = require('../config/constants');
const {generateFieldConfigs, shouldShowField} = require('../config/fields');
const {
  dataHelpers,
  validationHelpers,
  formatHelpers
} = require('../utils/helpers');
const {componentHelpers, eventHelpers} = require('../utils/component-helpers');
const {clone} = require('../../../utils/util');
const {hijack} = require('../../../utils/route');
const {debounce, handleSearchHight} = require('../../../utils/formate');
const {common} = require('../../../service/api');
const {hasPrivile} = require('../../../utils/route');

const app = getApp();

/**
 * 创建可配置的 Hunt Behavior
 * @param {Object} config - 配置选项
 * @returns {Object} Behavior 对象
 */
function createConfigurableBehavior(config) {
  config = config || {};
  const variant = config.variant || CONFIG_TYPES.FULL;
  const enableVipCheck =
    config.enableVipCheck !== undefined ? config.enableVipCheck : true;
  const enableSearch =
    config.enableSearch !== undefined ? config.enableSearch : true;
  const customMethods = config.customMethods || {};
  const customData = config.customData || {};

  // 获取对应变体的配置
  const searchConfig = getSearchConfig(variant);

  // 构建数据对象
  const behaviorData = {
    // 基础数据
    isIphoneX: app.globalData.isIphoneX,
    login: app.globalData.login,
    params: clone(searchConfig.defaultParams),

    // 输入框状态
    minCapital: '',
    maxCapital: '',
    dateType: '',
    dateActive: false,
    date: '',
    capitalActive: false,
    minDate: '',
    maxDate: '',
    socialActive: false,
    socialminPeson: '',
    socialmaxPeson: '',

    // 弹窗状态
    saveSearVal: '',
    searPop: false,
    saveSearPop: false,
    regionPop: false,
    datePop: false,
    eleseicPop: false,
    enttypePop: false,
    districtPop: false,
    chainCodePop: false,

    // 模板数据
    idName: '',
    focus: false,
    searchList: [],
    templateAry: [],
    renderAry: [],

    // 配置数据
    itemList: clone(searchConfig.searchTermList),
    leftList: clone(searchConfig.searchLeftLists)
  };

  // 合并自定义数据
  Object.keys(customData).forEach(function (key) {
    behaviorData[key] = customData[key];
  });

  return Behavior({
    properties: {
      wrapHeight: {
        type: String,
        value: ''
      },
      isPage: {
        type: Boolean,
        value: false
      },
      variant: {
        type: String,
        value: variant
      },
      filterAry: {
        type: Array,
        value: []
      }
    },

    data: behaviorData,

    observers: {
      params: function (val) {
        console.log('params changed:', val);
        this.result(val);
      }
    },

    methods: {
      /**
       * 清空搜索表单
       */
      clearSear: function() {
        const config = getSearchConfig(this.data.variant);
        const self = this;
        const list = config.searchTermList.map(function(item) {
          if (item.isOpenIcon) {
            const matchedItem = self.data.itemList.filter(function(i) {
              return item.type === i.type;
            })[0];
            item.isOpen = matchedItem ? matchedItem.isOpen : false;
          }
          return item;
        });

        this.setData({
          itemList: clone(list),
          params: clone(config.defaultParams),
          minCapital: '',
          maxCapital: '',
          minDate: '',
          maxDate: '',
          date: '',
          capitalActive: false,
          socialActive: false,
          socialminPeson: '',
          socialmaxPeson: '',
          dateActive: false,
          dateType: ''
        });
      },

      /**
       * 设置回填数据
       * @param {Object} tempObj - 回填数据对象
       */
      setBackfillData: function(tempObj) {
        tempObj = tempObj || {};
        this.clearSear();

        const data = this.data;
        let itemList = data.itemList;
        let params = data.params;
        const minCapital = data.minCapital;
        const maxCapital = data.maxCapital;
        const capitalActive = data.capitalActive;
        const socialActive = data.socialActive;
        const socialminPeson = data.socialminPeson;
        const socialmaxPeson = data.socialmaxPeson;
        const minDate = data.minDate;
        const maxDate = data.maxDate;
        const dateActive = data.dateActive;

        // 合并参数
        params = Object.assign(params, tempObj);

        // 处理回填逻辑
        const result = this._processBackfillData(itemList, params, {
          minCapital: minCapital,
          maxCapital: maxCapital,
          capitalActive: capitalActive,
          socialActive: socialActive,
          socialminPeson: socialminPeson,
          socialmaxPeson: socialmaxPeson,
          minDate: minDate,
          maxDate: maxDate,
          dateActive: dateActive
        });

        this.setData({
          params: params,
          itemList: result.updatedList,
          minCapital: result.minCapital,
          maxCapital: result.maxCapital,
          capitalActive: result.capitalActive,
          socialActive: result.socialActive,
          socialminPeson: result.socialminPeson,
          socialmaxPeson: result.socialmaxPeson,
          minDate: result.minDate,
          maxDate: result.maxDate,
          dateActive: result.dateActive
        });
      },

      /**
       * 处理回填数据的内部方法
       * @param {Array} itemList - 项目列表
       * @param {Object} params - 参数对象
       * @param {Object} inputStates - 输入框状态
       * @returns {Object} 处理后的数据
       */
      _processBackfillData: function(itemList, params, inputStates) {
        let minCapital = inputStates.minCapital;
        let maxCapital = inputStates.maxCapital;
        let capitalActive = inputStates.capitalActive;
        let socialActive = inputStates.socialActive;
        let socialminPeson = inputStates.socialminPeson;
        let socialmaxPeson = inputStates.socialmaxPeson;
        let minDate = inputStates.minDate;
        let maxDate = inputStates.maxDate;
        let dateActive = inputStates.dateActive;

        const self = this;
        const updatedList = itemList.map(function(item) {
          for (let key in params) {
            if (item.type === key) {
              // 处理不同类型的字段回填
              switch (item.type) {
                case 'reg_capital':
                  const result = self._handleCapitalBackfill(
                    params[key],
                    minCapital,
                    maxCapital,
                    capitalActive
                  );
                  minCapital = result.minCapital;
                  maxCapital = result.maxCapital;
                  capitalActive = result.capitalActive;
                  item = result.item || item;
                  break;

                case 'insured_num':
                  const socialResult = self._handleSocialBackfill(
                    params[key],
                    socialminPeson,
                    socialmaxPeson,
                    socialActive
                  );
                  socialminPeson = socialResult.socialminPeson;
                  socialmaxPeson = socialResult.socialmaxPeson;
                  socialActive = socialResult.socialActive;
                  item = socialResult.item || item;
                  break;

                case 'est_date':
                  const dateResult = self._handleDateBackfill(
                    params[key],
                    minDate,
                    maxDate,
                    dateActive
                  );
                  minDate = dateResult.minDate;
                  maxDate = dateResult.maxDate;
                  dateActive = dateResult.dateActive;
                  item = dateResult.item || item;
                  break;

                default:
                  item = self._handleDefaultBackfill(item, params[key]);
                  break;
              }
            }
          }
          return item;
        });

        return {
          updatedList: updatedList,
          minCapital: minCapital,
          maxCapital: maxCapital,
          capitalActive: capitalActive,
          socialActive: socialActive,
          socialminPeson: socialminPeson,
          socialmaxPeson: socialmaxPeson,
          minDate: minDate,
          maxDate: maxDate,
          dateActive: dateActive
        };
      },

      /**
       * 处理注册资本回填
       */
      _handleCapitalBackfill: function(
        paramValue,
        minCapital,
        maxCapital,
        capitalActive
      ) {
        const strs = paramValue.length
          ? paramValue[0].start + '$' + paramValue[0].end
          : '';
        const tagIdArr = ['0$100', '100$200', '500$1000', '1000$5000', '5000$'];

        if (!tagIdArr.includes(strs)) {
          minCapital = paramValue.length ? paramValue[0].start : '';
          maxCapital = paramValue.length ? paramValue[0].end : '';
          capitalActive = !!(minCapital || maxCapital);
        }

        return {
          minCapital: minCapital,
          maxCapital: maxCapital,
          capitalActive: capitalActive
        };
      },

      /**
       * 处理从业人数回填
       */
      _handleSocialBackfill(
        paramValue,
        socialminPeson,
        socialmaxPeson,
        socialActive
      ) {
        const strs = paramValue.length
          ? `${paramValue[0].start}$${paramValue[0].end}`
          : '';
        const tagIdArr = [
          '0$49',
          '50$99',
          '100$499',
          '500$999',
          '1000$4999',
          '5000$'
        ];

        if (!tagIdArr.includes(strs)) {
          socialminPeson = paramValue.length ? paramValue[0].start : '';
          socialmaxPeson = paramValue.length ? paramValue[0].end : '';
          socialActive = !!(socialminPeson || socialmaxPeson);
        }

        return {socialminPeson, socialmaxPeson, socialActive};
      },

      /**
       * 处理日期回填
       */
      _handleDateBackfill(paramValue, minDate, maxDate, dateActive) {
        const config = getSearchConfig(this.data.variant);
        const str = paramValue.length
          ? `${paramValue[0].start}$${paramValue[0].end}`
          : '';

        if (!config.dateTagList.includes(str)) {
          minDate = paramValue.length ? paramValue[0].start : '';
          maxDate = paramValue.length ? paramValue[0].end : '';
          dateActive = !!(minDate || maxDate);
        }

        return {minDate, maxDate, dateActive};
      },

      /**
       * 处理默认字段回填
       */
      _handleDefaultBackfill(item, paramValue) {
        // 这里可以根据字段类型进行不同的处理
        // 暂时保持原有逻辑
        return item;
      },

      /**
       * 验证搜索参数
       * @returns {boolean} 验证结果
       */
      validateSearchParams() {
        return validationHelpers.checkoutSear(this.data.params);
      },

      /**
       * 处理搜索结果
       * @param {Object} params - 搜索参数
       */
      result(params) {
        // 触发搜索结果事件
        this.triggerEvent('search', {
          params: dataHelpers.handleData(params || this.data.params)
        });
      },

      /**
       * 选择标签
       * @param {Object} event - 事件对象
       */
      selectTag: function (event) {
        const dataset = event.currentTarget.dataset;
        const id = dataset.id;
        const type = dataset.type;
        const name = dataset.name;
        const item = dataset.item;

        // VIP 检查
        if (enableVipCheck && item && item.vip) {
          const self = this;
          this._checkVipPermission().then(function (hasPermission) {
            if (!hasPermission) return;
            self._doSelectTag(id, type, name, item);
          });
          return;
        }

        this._doSelectTag(id, type, name, item);
      },

      /**
       * 执行标签选择逻辑
       */
      _doSelectTag: function (id, type, name, item) {
        const data = this.data;
        let itemList = data.itemList;
        let params = data.params;
        const SEARCH_CONSTANTS = require('../config/fields').SEARCH_CONSTANTS;

        // 更新标签状态
        const self = this;
        itemList = itemList.map(function (listItem) {
          if (listItem.type === type) {
            listItem.list = listItem.list.map(function (tag) {
              if (SEARCH_CONSTANTS.RADIO_FIELDS.includes(type)) {
                tag.active = false; // 单选先清空所有
              }

              if (tag.id === id) {
                if (SEARCH_CONSTANTS.RADIO_FIELDS.includes(type)) {
                  // 单选逻辑
                  if (params[type] == id) {
                    tag.active = false;
                    params[type] = [];
                  } else {
                    tag.active = true;
                    params[type] = [id];
                  }
                } else if (
                  SEARCH_CONSTANTS.MULTI_SELECT_FIELDS.includes(type)
                ) {
                  // 多选逻辑
                  let arr = params[type];
                  if (arr.includes(id)) {
                    arr.splice(
                      arr.findIndex(function (i) {
                        return i === id;
                      }),
                      1
                    );
                    tag.active = false;
                  } else {
                    arr.push(id);
                    tag.active = true;
                  }
                } else {
                  // 范围选择逻辑
                  self._handleRangeSelection(type, id, tag, params);
                }
              }
              return tag;
            });
          }
          return listItem;
        });

        this.setData({
          itemList: itemList,
          params: params
        });
      },

      /**
       * 处理范围选择
       */
      _handleRangeSelection(type, id, tag, params) {
        const obj = formatHelpers.splitStringToObject(id);
        params[type] = params[type].filter(i => !i.special);

        let arr = params[type];
        let idx = arr.findIndex(i =>
          type === 'est_date'
            ? i.start === obj.start && i.end === obj.end
            : JSON.stringify(i) === JSON.stringify(obj)
        );

        if (idx >= 0) {
          arr.splice(idx, 1);
          tag.active = false;
        } else {
          if (type === 'est_date') {
            obj.name = tag.name;
          }
          arr.push(obj);
          tag.active = true;
        }

        // 清空对应的输入框
        this._clearRelatedInputs(type);
      },

      /**
       * 清空相关输入框
       */
      _clearRelatedInputs(type) {
        const updates = {};

        switch (type) {
          case 'reg_capital':
            updates.minCapital = '';
            updates.maxCapital = '';
            updates.capitalActive = false;
            break;
          case 'insured_num':
            updates.socialminPeson = '';
            updates.socialmaxPeson = '';
            updates.socialActive = false;
            break;
          case 'est_date':
            updates.minDate = '';
            updates.maxDate = '';
            updates.dateActive = false;
            break;
        }

        if (Object.keys(updates).length > 0) {
          this.setData(updates);
        }
      },

      /**
       * 检查VIP权限
       */
      _checkVipPermission: function () {
        if (!enableVipCheck) {
          return Promise.resolve(true);
        }

        const self = this;
        return hasPrivile({packageType: true})
          .then(function (status) {
            if (status === '游客') {
              app.route(self, '/pages/login/login');
              return false;
            } else if (status === '普通VIP') {
              self.setData({vipVisible: true});
              return false;
            }
            return true;
          })
          .catch(function (error) {
            console.error('VIP permission check failed:', error);
            return true; // 默认允许
          });
      },

      /**
       * 输入框获取焦点
       */
      inputFocus(e) {
        const type = e.currentTarget.dataset.type;
        this._setTagStatus(type);
      },

      /**
       * 输入框值改变
       */
      inputChange(e) {
        const type = e.currentTarget.dataset.type;
        const {minCapital, maxCapital, socialminPeson, socialmaxPeson} =
          this.data;

        const updates = {};
        const paramKey = `params.${type}`;

        if (type === 'insured_num') {
          updates.socialActive = socialminPeson !== '' || socialmaxPeson !== '';
          updates[paramKey] = [
            {
              start: socialminPeson,
              end: socialmaxPeson,
              special: true
            }
          ];
        } else if (type === 'reg_capital') {
          updates.capitalActive = minCapital !== '' || maxCapital !== '';
          updates[paramKey] = [
            {
              start: minCapital,
              end: maxCapital,
              special: true
            }
          ];
        }

        const self = this;
        this.setData(updates, function () {
          self.result();
        });
      },

      /**
       * 设置标签状态
       */
      _setTagStatus(type) {
        let {itemList, params} = this.data;

        for (let item of itemList) {
          if (item.type === type) {
            item.list = item.list.map(tag => {
              tag.active = false;
              return tag;
            });
          }
        }

        params[type] = [];
        this.setData({itemList, params});
      },

      /**
       * 提交弹窗选择
       */
      submitSub(e) {
        const {itemList} = this.data;
        const {checkedList: obj, mark} = e.detail;
        const {POPUP_FIELD_MAPPING} = require('../config/fields');

        const mapping = POPUP_FIELD_MAPPING[mark];
        if (!mapping) return;

        const paramKey = `params.${mapping.data}`;
        const name = this._getNameFromPopup(obj);

        itemList.forEach(item => {
          if (item.type === mark) {
            item.content = name;
          }
        });

        this.setData(
          {
            [paramKey]: obj,
            itemList,
            regionPop: false,
            eleseicPop: false,
            enttypePop: false,
            districtPop: false
          },
          function () {
            self.result();
          }
        );
      },

      /**
       * 从弹窗数据获取名称
       */
      _getNameFromPopup: function(data) {
        if (!Array.isArray(data) || data.length === 0) return '';
        return data.map(function(item) {
          return item.name || item.label || item;
        }).join('、');
      }
    };

    // 合并自定义方法
    Object.keys(customMethods).forEach(function(key) {
      behaviorMethods[key] = customMethods[key];
    });

    return Behavior({
      properties: {
        wrapHeight: {
          type: String,
          value: ''
        },
        isPage: {
          type: Boolean,
          value: false
        },
        variant: {
          type: String,
          value: variant
        },
        filterAry: {
          type: Array,
          value: []
        }
      },

      data: behaviorData,

      observers: {
        params: function (val) {
          console.log('params changed:', val);
          this.result(val);
        }
      },

      methods: behaviorMethods
    });
}

// 导出模块
module.exports = createConfigurableBehavior;
