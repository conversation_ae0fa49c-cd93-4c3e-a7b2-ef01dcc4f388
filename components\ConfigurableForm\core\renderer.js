/**
 * 配置驱动的渲染逻辑模块
 * 支持基于配置的动态字段渲染
 */

const {getSearchConfig, CONFIG_TYPES} = require('../config/constants');
const {shouldShowField} = require('../config/fields');

/**
 * 生成渲染列表
 * @param {Object} config - 渲染配置
 * @returns {Array} 渲染列表
 */
function generateRenderList(config) {
  config = config || {};

  const variant = config.variant || CONFIG_TYPES.FULL;
  const excludeFields = config.excludeFields || [];
  const customFields = config.customFields || {};
  const context = config.context || {};

  // 获取搜索配置
  const searchConfig = getSearchConfig(variant);
  const searchLeftLists = searchConfig.searchLeftLists;
  const searchTermList = searchConfig.searchTermList;

  // 生成左侧菜单渲染列表
  const leftRenderList = generateLeftMenuList(searchLeftLists, {
    variant: variant,
    excludeFields: excludeFields,
    context: context
  });

  // 生成主要内容渲染列表
  const mainRenderList = generateMainContentList(searchTermList, {
    variant: variant,
    excludeFields: excludeFields,
    customFields: customFields,
    context: context
  });

  return {
    leftList: leftRenderList,
    itemList: mainRenderList,
    config: searchConfig
  };
}

/**
 * 生成左侧菜单列表
 * @param {Array} searchLeftLists - 左侧菜单配置
 * @param {Object} options - 选项
 * @returns {Array} 左侧菜单渲染列表
 */
function generateLeftMenuList(searchLeftLists, options) {
  options = options || {};
  const variant = options.variant;
  const excludeFields = options.excludeFields || [];
  const context = options.context || {};

  return searchLeftLists
    .map(function (group) {
      // 过滤掉被排除的字段
      const filteredEntries = Object.keys(group.map).filter(function (key) {
        // 检查是否应该显示该字段
        const contextWithVariant = Object.assign({}, context, {
          variant: variant
        });
        return (
          shouldShowField(key, contextWithVariant) &&
          !excludeFields.includes(key)
        );
      });

      const filteredMap = {};
      filteredEntries.forEach(function (key) {
        filteredMap[key] = group.map[key];
      });

      return {
        title: group.title,
        onlyText: true,
        isOpen: true,
        isActive: false,
        map: Object.keys(filteredMap).map(function (key) {
          return {
            key: key,
            value: filteredMap[key],
            active: false
          };
        })
      };
    })
    .filter(function (group) {
      return group.map.length > 0;
    }); // 过滤掉空组
}

/**
 * 生成主要内容列表
 * @param {Array} searchTermList - 搜索项配置
 * @param {Object} options - 选项
 * @returns {Array} 主要内容渲染列表
 */
function generateMainContentList(searchTermList, options = {}) {
  const {
    variant,
    excludeFields = [],
    customFields = {},
    context = {}
  } = options;

  // 过滤并处理搜索项
  const filteredList = searchTermList.filter(item => {
    return (
      shouldShowField(item.type, {variant, ...context}) &&
      !excludeFields.includes(item.type)
    );
  });

  // 应用自定义字段配置
  return filteredList.map(item => {
    const customConfig = customFields[item.type] || {};

    return {
      ...item,
      ...customConfig,
      // 确保列表项有正确的状态
      list: item.list
        ? item.list.map(listItem => ({
            ...listItem,
            active: false
          }))
        : undefined
    };
  });
}

/**
 * 根据左侧菜单和搜索项生成完整渲染列表
 * @param {Array} leftLists - 左侧菜单列表
 * @param {Array} allList - 所有搜索项列表
 * @returns {Array} 完整渲染列表
 */
export function generateCompleteRenderList(leftLists, allList) {
  const result = [];

  leftLists.forEach(leftItem => {
    // 添加左侧菜单项
    result.push({
      title: leftItem.title,
      onlyText: true,
      isOpen: true,
      isActive: false,
      map: Object.keys(leftItem.map).map(key => ({
        key,
        value: leftItem.map[key],
        active: false
      }))
    });

    // 添加对应的搜索项
    Object.keys(leftItem.map).forEach(itemKey => {
      const matchingItem = allList.find(item => item.type === itemKey);
      if (matchingItem) {
        result.push({
          ...matchingItem,
          list: matchingItem.list
            ? matchingItem.list.map(listItem => ({
                ...listItem,
                active: false
              }))
            : undefined
        });
      }
    });
  });

  return result;
}

/**
 * 更新渲染列表中的字段状态
 * @param {Array} renderList - 渲染列表
 * @param {string} fieldType - 字段类型
 * @param {Object} updates - 更新内容
 * @returns {Array} 更新后的渲染列表
 */
export function updateFieldInRenderList(renderList, fieldType, updates) {
  return renderList.map(item => {
    if (item.type === fieldType) {
      return {
        ...item,
        ...updates
      };
    }
    return item;
  });
}

/**
 * 更新渲染列表中的标签状态
 * @param {Array} renderList - 渲染列表
 * @param {string} fieldType - 字段类型
 * @param {string} tagId - 标签ID
 * @param {boolean} active - 是否激活
 * @returns {Array} 更新后的渲染列表
 */
export function updateTagInRenderList(renderList, fieldType, tagId, active) {
  return renderList.map(item => {
    if (item.type === fieldType && item.list) {
      return {
        ...item,
        list: item.list.map(tag => {
          if (tag.id === tagId) {
            return {...tag, active};
          }
          return tag;
        })
      };
    }
    return item;
  });
}

/**
 * 重置渲染列表中所有字段的状态
 * @param {Array} renderList - 渲染列表
 * @returns {Array} 重置后的渲染列表
 */
export function resetRenderListState(renderList) {
  return renderList.map(item => {
    const resetItem = {...item};

    // 重置标签状态
    if (resetItem.list) {
      resetItem.list = resetItem.list.map(tag => ({
        ...tag,
        active: false
      }));
    }

    // 重置内容
    if (resetItem.special === 'pop') {
      resetItem.content = '';
    }

    // 重置展开状态（保持原有的展开状态）
    if (resetItem.isOpenIcon) {
      // 保持原有状态，不重置
    }

    return resetItem;
  });
}

/**
 * 根据参数数据更新渲染列表状态
 * @param {Array} renderList - 渲染列表
 * @param {Object} params - 参数数据
 * @returns {Array} 更新后的渲染列表
 */
export function syncRenderListWithParams(renderList, params) {
  return renderList.map(item => {
    const paramValue = params[item.type];

    if (paramValue !== undefined && paramValue !== null) {
      const updatedItem = {...item};

      // 处理列表类型字段
      if (updatedItem.list && Array.isArray(paramValue)) {
        updatedItem.list = updatedItem.list.map(tag => ({
          ...tag,
          active: paramValue.includes(tag.id)
        }));
      }

      // 处理弹窗类型字段
      if (updatedItem.special === 'pop' && Array.isArray(paramValue)) {
        updatedItem.content = paramValue
          .map(item => item.name || item.label || item)
          .join('、');
      }

      // 处理输入类型字段
      if (updatedItem.special === 'input' && typeof paramValue === 'string') {
        updatedItem.content = paramValue;
      }

      return updatedItem;
    }

    return item;
  });
}

/**
 * 获取渲染列表中的字段配置
 * @param {Array} renderList - 渲染列表
 * @param {string} fieldType - 字段类型
 * @returns {Object|null} 字段配置
 */
function getFieldFromRenderList(renderList, fieldType) {
  return (
    renderList.find(function (item) {
      return item.type === fieldType;
    }) || null
  );
}

/**
 * 验证渲染列表的完整性
 * @param {Array} renderList - 渲染列表
 * @returns {Object} 验证结果
 */
function validateRenderList(renderList) {
  const errors = [];
  const warnings = [];

  renderList.forEach(function (item, index) {
    // 检查必要字段
    if (!item.type) {
      errors.push('Item at index ' + index + " missing required 'type' field");
    }

    if (!item.title) {
      warnings.push('Item at index ' + index + " missing 'title' field");
    }

    // 检查列表项的完整性
    if (item.list) {
      item.list.forEach(function (listItem, listIndex) {
        if (!listItem.id) {
          errors.push(
            'List item at ' + index + '.' + listIndex + " missing 'id' field"
          );
        }

        if (!listItem.name) {
          warnings.push(
            'List item at ' + index + '.' + listIndex + " missing 'name' field"
          );
        }
      });
    }
  });

  return {
    isValid: errors.length === 0,
    errors: errors,
    warnings: warnings
  };
}

// 导出模块
module.exports = {
  generateRenderList: generateRenderList,
  generateLeftMenuList: generateLeftMenuList,
  generateMainContentList: generateMainContentList,
  generateCompleteRenderList: generateCompleteRenderList,
  updateFieldInRenderList: updateFieldInRenderList,
  updateTagInRenderList: updateTagInRenderList,
  resetRenderListState: resetRenderListState,
  syncRenderListWithParams: syncRenderListWithParams,
  getFieldFromRenderList: getFieldFromRenderList,
  validateRenderList: validateRenderList
};
