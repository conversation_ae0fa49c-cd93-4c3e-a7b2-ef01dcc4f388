/**
 * 最基础的测试组件
 * 只测试最核心的导入功能
 */

Component({
  data: {
    testResult: '开始测试...'
  },
  
  methods: {
    testBasicImport: function() {
      let results = [];
      
      // 测试 constants 导入
      try {
        const constants = require('./config/constants');
        if (constants && constants.CONFIG_TYPES) {
          results.push('✅ constants 导入成功');
        } else {
          results.push('❌ constants 导入失败 - 无数据');
        }
      } catch (error) {
        results.push('❌ constants 导入失败: ' + error.message);
      }
      
      // 测试 behavior-simple 导入
      try {
        const createBehavior = require('./core/behavior-simple');
        if (typeof createBehavior === 'function') {
          results.push('✅ behavior-simple 导入成功');
        } else {
          results.push('❌ behavior-simple 导入失败 - 不是函数');
        }
      } catch (error) {
        results.push('❌ behavior-simple 导入失败: ' + error.message);
      }
      
      // 测试创建 behavior
      try {
        const createBehavior = require('./core/behavior-simple');
        const behavior = createBehavior();
        if (behavior) {
          results.push('✅ behavior 创建成功');
        } else {
          results.push('❌ behavior 创建失败');
        }
      } catch (error) {
        results.push('❌ behavior 创建失败: ' + error.message);
      }
      
      this.setData({
        testResult: results.join('\n')
      });
      
      console.log('测试结果:', results);
    }
  },
  
  ready: function() {
    console.log('基础测试组件就绪');
    this.testBasicImport();
  }
});
