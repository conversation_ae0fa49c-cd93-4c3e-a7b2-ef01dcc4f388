/**
 * 简化测试组件
 * 用于验证基本的导入导出是否正常工作
 */

// 测试导入
try {
  const {CONFIG_TYPES} = require('./config/constants');
  console.log('constants 导入成功:', CONFIG_TYPES);
} catch (error) {
  console.error('constants 导入失败:', error);
}

try {
  const {FIELD_TYPES} = require('./config/fields');
  console.log('fields 导入成功:', FIELD_TYPES);
} catch (error) {
  console.error('fields 导入失败:', error);
}

try {
  const {dataHelpers} = require('./utils/helpers');
  console.log('helpers 导入成功:', dataHelpers);
} catch (error) {
  console.error('helpers 导入失败:', error);
}

try {
  const createConfigurableBehavior = require('./core/behavior-simple');
  console.log('behavior 导入成功:', typeof createConfigurableBehavior);
} catch (error) {
  console.error('behavior 导入失败:', error);
}

try {
  const {generateRenderList} = require('./core/renderer');
  console.log('renderer 导入成功:', typeof generateRenderList);
} catch (error) {
  console.error('renderer 导入失败:', error);
}

// 简化的组件定义
Component({
  data: {
    testMessage: '导入测试组件'
  },

  methods: {
    testImports: function () {
      console.log('开始测试导入...');

      // 测试配置
      try {
        const {getSearchConfig, CONFIG_TYPES} = require('./config/constants');
        const config = getSearchConfig(CONFIG_TYPES.FULL);
        console.log('配置测试成功:', config ? '有数据' : '无数据');
      } catch (error) {
        console.error('配置测试失败:', error);
      }

      // 测试工具方法
      try {
        const {dataHelpers} = require('./utils/helpers');
        const testData = {test: 'value'};
        const result = dataHelpers.deepClone(testData);
        console.log('工具方法测试成功:', result);
      } catch (error) {
        console.error('工具方法测试失败:', error);
      }

      console.log('导入测试完成');
    }
  },

  ready: function () {
    console.log('测试组件就绪');
    this.testImports();
  }
});
