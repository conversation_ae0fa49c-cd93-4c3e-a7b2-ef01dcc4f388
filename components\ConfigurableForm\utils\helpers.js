/**
 * 通用工具方法库
 * 提取可复用的工具方法，减少代码重复
 */

const {clone} = require('../../../utils/util');
const {
  SEARCH_CONSTANTS,
  POPUP_FIELD_MAPPING,
  RANGE_INPUT_TIPS
} = require('../config/fields');

/**
 * 数据处理工具
 */
const dataHelpers = {
  /**
   * 处理搜索参数数据
   * @param {Object} params - 参数对象
   * @returns {Object} 处理后的参数
   */
  handleData(params) {
    let result = clone(params);

    // 处理布尔值字段
    SEARCH_CONSTANTS.BOOL_FIELDS.forEach(field => {
      if (result[field]?.length) {
        result[field] = JSON.parse(result[field][0]);
      } else {
        delete result[field];
      }
    });

    // 处理带前缀的字段
    const parentTypes = ['super_dimension_'];
    parentTypes.forEach(prefix => {
      Object.keys(result).forEach(key => {
        if (key.startsWith(prefix)) {
          const suffix = key.slice(prefix.length);
          const parentKey = key.slice(0, prefix.length - 1);
          const obj = {};
          obj[suffix] = result[key];
          result[parentKey] = {
            ...result[parentKey],
            ...obj
          };
          delete result[key];
        }
      });
    });

    return result;
  },

  /**
   * 转换参数结构
   * @param {Object} data - 原始数据
   * @returns {Object} 转换后的数据
   */
  handlestructure(data) {
    return this.handleData(data);
  },

  /**
   * 转换参数格式
   * @param {Object} params - 参数对象
   * @returns {Object} 转换后的参数
   */
  transformParams(params) {
    const result = {};

    Object.keys(params).forEach(key => {
      if (Array.isArray(params[key]) && params[key].length > 0) {
        result[key] = params[key];
      } else if (typeof params[key] === 'string' && params[key].trim() !== '') {
        result[key] = params[key];
      }
    });

    return result;
  },

  /**
   * 深度克隆对象
   * @param {any} obj - 要克隆的对象
   * @returns {any} 克隆后的对象
   */
  deepClone(obj) {
    return JSON.parse(JSON.stringify(obj));
  }
};

/**
 * 验证工具
 */
const validationHelpers = {
  /**
   * 检查搜索参数
   * @param {Object} params - 参数对象
   * @returns {boolean} 验证结果
   */
  checkoutSear(params) {
    let hasError = false;

    Object.keys(params).forEach(key => {
      if (params[key]?.length > 0) {
        // 检查范围输入字段
        if (SEARCH_CONSTANTS.RANGE_INPUT_FIELDS.includes(key)) {
          const item = params[key][0];
          if (
            item.start &&
            item.end &&
            parseFloat(item.start) >= parseFloat(item.end)
          ) {
            const tips = RANGE_INPUT_TIPS[key];
            wx.showToast({
              title: `${tips.min}不能大于等于${tips.max}`,
              icon: 'none'
            });
            hasError = true;
          }
        }
      }
    });

    return !hasError;
  },

  /**
   * 验证单个字段
   * @param {string} fieldType - 字段类型
   * @param {any} value - 字段值
   * @param {Object} rules - 验证规则
   * @returns {Object} 验证结果
   */
  validateField(fieldType, value, rules) {
    rules = rules || {};
    const result = {valid: true, message: ''};

    // 必填验证
    if (
      rules.required &&
      (!value || (Array.isArray(value) && value.length === 0))
    ) {
      result.valid = false;
      result.message = '此字段为必填项';
      return result;
    }

    // 字符串长度验证
    if (
      rules.maxLength &&
      typeof value === 'string' &&
      value.length > rules.maxLength
    ) {
      result.valid = false;
      result.message = `字符长度不能超过${rules.maxLength}`;
      return result;
    }

    // 数字范围验证
    if (rules.type === 'number' && value !== '') {
      const numValue = parseFloat(value);
      if (isNaN(numValue)) {
        result.valid = false;
        result.message = '请输入有效数字';
        return result;
      }

      if (rules.min !== undefined && numValue < rules.min) {
        result.valid = false;
        result.message = `数值不能小于${rules.min}`;
        return result;
      }

      if (rules.max !== undefined && numValue > rules.max) {
        result.valid = false;
        result.message = `数值不能大于${rules.max}`;
        return result;
      }
    }

    return result;
  },

  /**
   * 验证整个表单
   * @param {Object} formData - 表单数据
   * @param {Object} fieldConfigs - 字段配置
   * @returns {Object} 验证结果
   */
  validateForm(formData, fieldConfigs) {
    const errors = {};
    let isValid = true;

    Object.keys(fieldConfigs).forEach(fieldType => {
      const config = fieldConfigs[fieldType];
      const value = formData[fieldType];
      const validation = this.validateField(
        fieldType,
        value,
        config.validation
      );

      if (!validation.valid) {
        errors[fieldType] = validation.message;
        isValid = false;
      }
    });

    return {isValid, errors};
  }
};

/**
 * 格式化工具
 */
const formatHelpers = {
  /**
   * 格式化日期
   * @param {string|Date} date - 日期
   * @param {string} format - 格式
   * @returns {string} 格式化后的日期
   */
  formatDate(date, format) {
    format = format || 'YYYY-MM-DD';
    if (!date) return '';

    const d = new Date(date);
    const year = d.getFullYear();
    const month = String(d.getMonth() + 1).padStart(2, '0');
    const day = String(d.getDate()).padStart(2, '0');

    return format.replace('YYYY', year).replace('MM', month).replace('DD', day);
  },

  /**
   * 格式化货币
   * @param {number} amount - 金额
   * @param {string} unit - 单位
   * @returns {string} 格式化后的货币
   */
  formatCurrency(amount, unit) {
    unit = unit || '万元';
    if (amount === undefined || amount === null || amount === '') return '';

    const num = parseFloat(amount);
    if (isNaN(num)) return amount;

    return `${num.toLocaleString()}${unit}`;
  },

  /**
   * 格式化选项列表
   * @param {Array} options - 选项数组
   * @returns {string} 格式化后的字符串
   */
  formatOptions(options) {
    if (!Array.isArray(options) || options.length === 0) return '';

    return options
      .map(option => option.name || option.label || option)
      .join('、');
  },

  /**
   * 分割字符串为对象
   * @param {string} str - 字符串
   * @param {string} separator - 分隔符
   * @returns {Object} 分割后的对象
   */
  splitStringToObject(str, separator) {
    separator = separator || '$';
    const obj = {start: '', end: ''};

    if (str && str.indexOf(separator) > -1) {
      const arr = str.split(separator);
      obj.start = arr[0] || '';
      obj.end = arr[1] || '';
    }

    return obj;
  },

  /**
   * 对象转换为字符串
   * @param {Object} obj - 对象
   * @param {string} separator - 分隔符
   * @returns {string} 转换后的字符串
   */
  objectToString(obj, separator) {
    separator = separator || '$';
    if (!obj || typeof obj !== 'object') return '';

    const start = obj.start || '';
    const end = obj.end || '';

    return `${start}${separator}${end}`;
  }
};

/**
 * 通用工具函数
 */
const utils = {
  /**
   * 防抖函数
   * @param {Function} func - 要防抖的函数
   * @param {number} delay - 延迟时间
   * @returns {Function} 防抖后的函数
   */
  debounce(func, delay) {
    delay = delay || 300;
    let timeoutId;
    return function () {
      const args = Array.prototype.slice.call(arguments);
      const self = this;
      clearTimeout(timeoutId);
      timeoutId = setTimeout(function () {
        func.apply(self, args);
      }, delay);
    };
  },

  /**
   * 节流函数
   * @param {Function} func - 要节流的函数
   * @param {number} delay - 延迟时间
   * @returns {Function} 节流后的函数
   */
  throttle(func, delay) {
    delay = delay || 300;
    let lastTime = 0;
    return function () {
      const args = Array.prototype.slice.call(arguments);
      const self = this;
      const now = Date.now();
      if (now - lastTime >= delay) {
        lastTime = now;
        func.apply(self, args);
      }
    };
  },

  /**
   * 生成唯一ID
   * @returns {string} 唯一ID
   */
  generateId() {
    return Date.now().toString(36) + Math.random().toString(36).substr(2);
  },

  /**
   * 检查是否为空值
   * @param {any} value - 值
   * @returns {boolean} 是否为空
   */
  isEmpty(value) {
    if (value === null || value === undefined) return true;
    if (typeof value === 'string') return value.trim() === '';
    if (Array.isArray(value)) return value.length === 0;
    if (typeof value === 'object') return Object.keys(value).length === 0;
    return false;
  }
};

// 导出模块
module.exports = {
  dataHelpers: dataHelpers,
  validationHelpers: validationHelpers,
  formatHelpers: formatHelpers,
  utils: utils
};
