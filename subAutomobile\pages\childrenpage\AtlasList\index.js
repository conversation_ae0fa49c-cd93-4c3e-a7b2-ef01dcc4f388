import {chain, common} from '../../../../service/api';
import {setTagColor} from '../../../../utils/util.js';
import {getHeight} from '../../../../utils/height.js';
import {preventActive} from '../../../../utils/util';
import {getPx} from '../../../../utils/formate';
import {collect} from '../../../utils/mixin/collect';
import {
  checkoutSear,
  clearChildComponent,
  closeEntNamePop,
  handlestructure,
  handleData,
  fillChildComponent
} from '../../../../components/huntCopy/mixin';
const app = getApp();
Page({
  data: {
    company_num: 0,
    //请求的相关
    paramsNew: {
      chain_codes: [],
      page_index: 1,
      page_size: 10
    },
    oldParams: {}, //用来反向填充的
    requestData: [],
    hasData: true, // 判断接口是否还有数据返回
    isTriggered: false, // 下拉刷新状态
    head_touch: undefined,
    sortVisible: false,
    popSort: [
      {
        name: '筛选',
        show: false,
        isVal: false
      },
      {
        name: '排序',
        show: false,
        isVal: false
      }
    ],
    sortList: [
      {
        name: '成立日期从晚到早',
        type: 'ESDATE',
        order: 'DESC',
        show: false
      },
      {
        name: '成立日期从早到晚',
        type: 'ESDATE',
        order: 'ASC',
        show: false
      },
      {
        name: '注册资本从高到低',
        type: 'REGCAP',
        order: 'DESC',
        show: false
      },
      {
        name: '注册资本从低到高',
        type: 'REGCAP',
        order: 'ASC',
        show: false
      }
    ],
    // 路由 -返回是这个又应该怎么变化呢 到时候看传进来的数据结构来改 4个长度会有问题
    routeList: [],

    // 弹窗相关
    popType: '',
    showVisible: false,
    title: '',
    content: '',
    cancelBtnText: '取消',
    confirmBtnText: '删除',
    cnacelEnt: '',
    popIndex: 0,
    // 联系方式
    showContact: false,
    contactList: [],
    // 地址
    showAddress: false,
    markers: [],
    locationTxt: '',
    location: {
      lat: '',
      lon: ''
    },
    chain_codes: [],
    tagArr: []
  },
  onLoad: function (options) {
    let arr = JSON.parse(decodeURIComponent(options.arr));
    let tagArr = [];
    let {code, high, strong_codes, weak_codes, leader_count} =
      arr[arr.length - 1];
    if (arr.length > 1) {
      high == '1' && tagArr.push('高价值产业环节');
      leader_count && tagArr.push('有龙头企业');
      if (wx.getStorageSync('preset')) {
        let preset = JSON.parse(wx.getStorageSync('preset'));
        preset.carrier_area_code_list.length &&
          weak_codes.includes(preset.carrier_area_code_list[0]) &&
          tagArr.push('弱势环节');
        preset.carrier_area_code_list.length &&
          strong_codes.includes(preset.carrier_area_code_list[0]) &&
          tagArr.push('优势环节');
      }
    }
    arr[0].back = true;
    let {paramsNew} = this.data;
    // 这个不管怎样都要带上 所以写死
    paramsNew.chain_codes = [code];
    this.setData({
      paramsNew,
      chain_codes: [code],
      routeList: arr,
      tagArr: tagArr
    });
  },
  headTap(e) {
    let {item} = e.currentTarget.dataset;
    let {popSort} = this.data;
    let isshow = !popSort[item].show;
    popSort.forEach(item => {
      item.show = false;
    });
    popSort[item].show = isshow;
    if (item === 0) {
      fillChildComponent(this)(this.data.oldParams);
    }
    this.setData({
      popSort
    });
  },
  chooseSort(e) {
    let {item} = e.currentTarget.dataset;
    let {sortList, paramsNew, popSort} = this.data;
    paramsNew.sort = {
      name: sortList[item].type,
      order: sortList[item].order
    };
    let isshow = !sortList[item].show;
    sortList.forEach(item => {
      item.show = false;
    });
    sortList[item].show = isshow;
    if (!isshow) {
      paramsNew.sort = undefined;
      popSort[1].isVal = false;
    } else {
      popSort[1].isVal = true;
    }
    popSort[1].show = false;
    this.setData({
      sortList,
      paramsNew,
      popSort
    });
    this.quest();
  },
  closeSort1() {
    let {popSort} = this.data;
    popSort[1].show = false;
    this.setData({
      popSort
    });
  },
  closeSort0() {
    let {popSort} = this.data;
    popSort[0].show = false;
    this.setData({
      popSort
    });
  },
  // 下拉刷新
  handleRefresher() {
    let {paramsNew} = this.data;
    paramsNew.page_index = 1;
    this.setData({
      paramsNew,
      requestData: [],
      isTriggered: true
    });
    this.quest();
  },
  //加载更多
  loadMore() {
    let {paramsNew, hasData} = this.data;
    if (!hasData) {
      wx.showToast({
        title: '已经到底啦',
        icon: 'error'
      });
      return;
    }
    paramsNew.page_index += 1;
    this.setData({
      paramsNew
    });
    this.quest(true);
  },

  async quest(bl) {
    wx.showLoading({
      title: '正在加载',
      mask: true
    });
    let {paramsNew, hasData, requestData} = this.data;
    paramsNew['chain_codes'] = this.data.chain_codes;
    hasData = true;
    Object.keys(paramsNew).forEach(item => {
      if (
        ![
          'reg_capital',
          'areas',
          'ent_size',
          'est_date',
          'trade_types',
          'benefits_assess',
          'page_index',
          'page_size',
          'sort',
          'chain_codes'
        ].includes(item) &&
        paramsNew[item]
      ) {
        delete paramsNew[item];
      }
    });
    delete paramsNew?.['ent_name'];

    let {items, count} = await chain.chainDetail(paramsNew);
    items = items.map(item => {
      item.tags = setTagColor(item.tags);
      return item;
    });
    items.forEach(item => {
      item.tags = item.tags.splice(0, 3);
    });
    if (items.length < paramsNew.page_size || count == paramsNew.page_size)
      hasData = false;
    this.setData({
      hasData,
      company_num: count,
      requestData: bl ? requestData.concat(items) : items,
      isTriggered: false
    });
    wx.hideLoading();
  },
  onClose() {
    this.setData({
      showVisible: false
    });
  },
  onCloseContact() {
    this.setData({
      showContact: false
    });
  },
  onCloseAddress() {
    this.setData({
      showAddress: false
    });
  },
  onShow() {
    this.handleHeight();
    this.quest();
  },
  // 卡片点击回调
  async onCard(data) {
    let that = this;
    preventActive(this, async () => {
      // 地址  "site"  联系方式 "relation" 官网 "official" 收藏 "collect" 四个字段对呀四个方法逻辑-具体见设计图
      const type = data.detail.type;
      const comDetail = data.detail.data;
      // 处理收藏
      if (type == 'collect') {
        collect(that, comDetail, 'requestData');
      } else if (type == 'relation') {
        let contactRes = await common.contact(
          comDetail.ent_id,
          encodeURI('types=1,2')
        );
        this.setData({
          contactList: contactRes,
          showContact: true
        });
      } else if (type === 'site') {
        this.setData({
          location: comDetail.location,
          locationTxt: comDetail.register_address,
          markers: [
            {
              id: 1,
              latitude: comDetail.location.lat,
              longitude: comDetail.location.lon,
              iconPath:
                'https://hd-custom.oss-cn-beijing.aliyuncs.com/cdn/images/app/imgs/image/marker.png',
              width: 20,
              height: 20
            }
          ],
          showAddress: true
        });
      }
    });
  },
  // 动态获取蒙层高度
  handleHeight() {
    const that = this;
    getHeight(that, '.page_top', data => {
      this.setData({
        computedHeight: data.screeHeight - data.res[0].height + getPx(96)
      });
    });
  },

  onexpPop() {
    this.setData({
      isExpPop: true
    });
  },
  handleHeightParams(heightParams) {
    //格式化高级筛选的参数
    let params = Object.assign({}, heightParams); //拷贝
    // 高级筛选-第一次请求-合并参数
    delete params['all_cert_data'];
    delete params['eleseic_data'];
    delete params['enttype_data'];
    delete params['regionData'];
    params.contacts_style = params.contacts_style || [];
    params.fixed_phone_flag = params.fixed_phone_flag || [];

    params['fixed_phone_flag'] = Array.from(
      new Set([...params.fixed_phone_flag, ...params.contacts_style])
    );

    delete params['contacts_style'];
    // 联系方式为none则不传
    let idx = params['fixed_phone_flag'].indexOf('none');
    if (idx > -1) {
      params['fixed_phone_flag'].splice(idx, 1);
    }
    if (params['est_date'].length) {
      params['est_date'].map(i => {
        delete i['name'];
      });
    }
    params = handlestructure(params);
    return params;
  },

  // 重置筛选条件
  resetCondition() {
    clearChildComponent(this)();
  },
  getParams(e) {
    // 筛选后获取的数据
    let {isHeight, paramsData} = e.detail;
    this.setData({
      isHeight,
      paramsData
    });
  },
  // 查询
  search() {
    let {
      paramsNew: {page_index, page_size, sort = {}}
    } = this.data; // 当前页参数
    let {paramsData} = this.data;
    page_index = 1;
    closeEntNamePop(this)();
    if (!checkoutSear(paramsData)) {
      return;
    }
    let temObj = handleData(paramsData);
    paramsData = JSON.parse(temObj);
    temObj = {
      page_index,
      page_size,
      ...this.handleHeightParams(JSON.parse(temObj))
    };
    if (Object.keys(sort).length) {
      temObj['sort'] = sort;
    }
    this.setData(
      {
        paramsNew: temObj,
        oldParams: paramsData,
        'popSort[0].show': false
      },
      () => {
        let bool = [
          'areas',
          'benefits_assess',
          'ent_size',
          'reg_capital',
          'est_date',
          'trade_types'
        ].some(item => this.data.paramsNew[item].length > 0);
        if (bool) {
          this.setData({
            'popSort[0].isVal': true
          });
        } else {
          this.setData({
            'popSort[0].isVal': false
          });
        }
        this.quest();
      }
    );
  },
  // 返回路由
  backRoute(e) {
    let {item, index, ditu} = e.currentTarget.dataset;
    let {routeList} = this.data;
    let arr = [];
    // 最后一个不跳

    // 地图展示跳到对于路由
    if (ditu) {
      // 跳到地图页面
      arr = routeList;
      app.route(
        this,
        `/automobile/pages/childrenpage/AtlasMapList/index?arr=${encodeURIComponent(
          JSON.stringify(arr)
        )}`
      );
      return;
    }

    if (item.back) {
      app.route(this, 1, 'navigateBack');
      return;
    }
    let idx = routeList.findIndex(i => i.code == item.code);
    if (item.code) {
      arr = routeList.slice(0, idx + 1);
      // 刷新当前页面
      this.setData(
        {
          company_num: 0,
          //请求的相关
          paramsNew: {
            chain_codes: [],
            page_index: 1,
            page_size: 10
          },
          oldParams: {}, //用来反向填充的
          requestData: [],
          hasData: true, // 判断接口是否还有数据返回
          isTriggered: false, // 下拉刷新状态
          head_touch: undefined,
          sortVisible: false,
          popSort: [
            {
              name: '筛选',
              show: false,
              isVal: false
            },
            {
              name: '排序',
              show: false,
              isVal: false
            }
          ],
          sortList: [
            {
              name: '成立日期从晚到早',
              type: 'ESDATE',
              order: 'DESC',
              show: false
            },
            {
              name: '成立日期从早到晚',
              type: 'ESDATE',
              order: 'ASC',
              show: false
            },
            {
              name: '注册资本从高到低',
              type: 'REGCAP',
              order: 'DESC',
              show: false
            },
            {
              name: '注册资本从低到高',
              type: 'REGCAP',
              order: 'ASC',
              show: false
            }
          ],
          // 路由 -返回是这个又应该怎么变化呢 到时候看传进来的数据结构来改 4个长度会有问题
          routeList: [],

          // 弹窗相关
          popType: '',
          showVisible: false,
          title: '',
          content: '',
          cancelBtnText: '取消',
          confirmBtnText: '删除',
          cnacelEnt: '',
          popIndex: 0,
          // 联系方式
          showContact: false,
          contactList: [],
          // 地址
          showAddress: false,
          markers: [],
          locationTxt: '',
          location: {
            lat: '',
            lon: ''
          },
          chain_codes: [],
          tagArr: []
        },
        () => {
          this.onLoad({
            arr: decodeURIComponent(JSON.stringify(arr))
          });
          this?.handleHeight();
          this.quest();
        }
      );
    }
  }
});
